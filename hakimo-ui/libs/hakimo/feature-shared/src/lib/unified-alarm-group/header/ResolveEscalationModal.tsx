import {
  useResolveLocationAlarmIncident,
  useUpdateLocationAlarm,
  useUpdateAlarmGroupState,
} from '@hakimo-ui/hakimo/data-access';
import { Escalation, StatusType } from '@hakimo-ui/hakimo/types';
import {
  eventTracker,
  useCanUpdateLocationAlarmStatus,
  useCanUpdateAlarmGroupStatus,
  useUser,
} from '@hakimo-ui/hakimo/util';
import { Alert, Button, Modal, Textarea } from '@hakimo-ui/shared/ui-base';
import dayjs from 'dayjs';
import { useState } from 'react';

interface Props {
  alarmId: string;
  escalation: Escalation;
  onStatusChange?: (status: StatusType) => void;
  onClose: () => void;
  product?: 'scan' | 'remote-guarding';
}

export function ResolveEscalationModal(props: Props) {
  const { alarmId, escalation, onClose, onStatusChange, product } = props;
  const [textVal, setTextVal] = useState('');

  const user = useUser();
  const canUpdateLocationAlarmStatus = useCanUpdateLocationAlarmStatus();
  const canUpdateAlarmGroupStatus = useCanUpdateAlarmGroupStatus();

  const onSuccess = () => {
    onStatusChange && onStatusChange(StatusType.RESOLVED);
  };

  const resolveAlarmMutation = useUpdateLocationAlarm(alarmId, onSuccess);
  const resolveIncidentMutation = useResolveLocationAlarmIncident();
  const resolveAlarmGroupMutation = useUpdateAlarmGroupState(alarmId, onSuccess);

  const onTextChange = (val: string) => setTextVal(val);

  const onSubmit = () => {
    if (product === 'scan') {
      const resolveAlarmGroupPayload = {
        resolution: 'escalation_close' as const,
        resolutionComment: textVal,
      };
      resolveAlarmGroupMutation.mutate(resolveAlarmGroupPayload);
    } else {
      const locationIncidentPayload = {
        incident_id: parseInt(escalation.id),
        incident_end_time_utc: dayjs().utc().format('YYYY-MM-DDTHH:mm:ss'),
        resolved_by: user.id,
        resolution_comment: textVal,
      };
      resolveIncidentMutation.mutate(locationIncidentPayload);

      const resolveAlarmPayload = {
        status: StatusType.RESOLVED,
        comment: `Escalation - ${textVal}`,
      };
      resolveAlarmMutation.mutate(resolveAlarmPayload);
    }
  };

  const canUpdateStatus = product === 'scan' ? canUpdateAlarmGroupStatus : canUpdateLocationAlarmStatus;
  const isLoading = product === 'scan'
    ? resolveAlarmGroupMutation.isLoading
    : resolveAlarmMutation.isLoading || resolveIncidentMutation.isLoading;

  const actions = (
    <>
      {canUpdateStatus && (
        <Button
          title="Resolve"
          variant="primary"
          disabled={textVal === ''}
          onClick={onSubmit}
          loading={isLoading}
          onSideEffect={eventTracker(
            'close_and_resolve_active_location_incident'
          )}
        >
          Resolve
        </Button>
      )}
      <Button onClick={onClose}>Cancel</Button>
    </>
  );

  return (
    <Modal
      title="Resolve Incident"
      onClose={onClose}
      open
      closable
      footer={actions}
    >
      <div className="space-y-4 p-8">
        {(product === 'scan' ? resolveAlarmGroupMutation.isError :
          resolveAlarmMutation.isError || resolveIncidentMutation.isError) && (
            <Alert type="error">
              Error submitting and resolving the incident
            </Alert>
          )}
        <Alert type="info">
          Alarm should only be resolved once site POC and PD are alerted about
          the incident.
        </Alert>
        <div>Provide a brief comment on the escalation</div>
        <Textarea
          value={textVal}
          rows={4}
          placeholder="Describe the incident and the actions taken"
          onChange={onTextChange}
        />
      </div>
    </Modal>
  );
}

export default ResolveEscalationModal;
